// Test setup file
const dotenv = require('dotenv');

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Mock DBOS logger to prevent console spam during tests
jest.mock('@dbos-inc/dbos-sdk', () => ({
  DBOS: {
    logger: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    },
    transaction: jest.fn().mockReturnValue(jest.fn()),
    step: jest.fn().mockReturnValue(jest.fn()),
    workflow: jest.fn().mockReturnValue(jest.fn()),
    knexClient: jest.fn().mockImplementation(() => ({
      where: jest.fn().mockReturnThis(),
      count: jest.fn().mockReturnThis(),
      first: jest.fn().mockResolvedValue({ count: '0' }),
      select: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockReturnThis(),
      del: jest.fn().mockReturnThis(),
      distinct: jest.fn().mockReturnThis(),
      whereNotIn: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    })),
    setConfig: jest.fn(),
    launch: jest.fn(),
    startWorkflow: jest.fn().mockImplementation((operationsClass) => {
      // Create a sophisticated workflow mock that delegates to the actual service mocks
      const workflowMethods = {
        // Session Management
        createSession: jest.fn().mockImplementation(async (request) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.createSession(request);
          })
        })),

        getSession: jest.fn().mockImplementation(async (sessionId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.getSession(sessionId);
          })
        })),

        updateSessionActivity: jest.fn().mockImplementation(async (sessionId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.updateSessionActivity(sessionId);
          })
        })),

        updateSessionConversation: jest.fn().mockImplementation(async (sessionId, conversationId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.updateSessionConversation(sessionId, conversationId);
          })
        })),

        deleteSession: jest.fn().mockImplementation(async (sessionId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.deleteSession(sessionId);
          })
        })),

        extendSession: jest.fn().mockImplementation(async (sessionId, hours) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.extendSession(sessionId, hours);
          })
        })),

        cleanupExpiredSessions: jest.fn().mockImplementation(async () => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionService = require('../src/core/SessionService').SessionService;
            return await SessionService.cleanupExpiredSessions();
          })
        })),

        // Conversation Management
        getConversationBySession: jest.fn().mockImplementation(async (sessionId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const ConversationService = require('../src/core/ConversationService').ConversationService;
            return await ConversationService.getConversationBySession(sessionId);
          })
        })),

        getConversationMessages: jest.fn().mockImplementation(async (conversationId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const ConversationService = require('../src/core/ConversationService').ConversationService;
            return await ConversationService.getConversationMessages(conversationId);
          })
        })),

        getActiveSessionsForUser: jest.fn().mockImplementation(async (userIdentifier) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const ConversationService = require('../src/core/ConversationService').ConversationService;
            return await ConversationService.getActiveSessionsForUser(userIdentifier);
          })
        })),

        // Cleanup Operations
        getCleanupStats: jest.fn().mockImplementation(async () => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionCleanupService = require('../src/core/SessionCleanupService').SessionCleanupService;
            return await SessionCleanupService.getCleanupStats();
          })
        })),

        manualSessionCleanup: jest.fn().mockImplementation(async (options) => ({
          getResult: jest.fn().mockImplementation(async () => {
            const SessionCleanupService = require('../src/core/SessionCleanupService').SessionCleanupService;
            return await SessionCleanupService.manualCleanup(options);
          })
        })),

        // Chat Operations
        chatWorkflow: jest.fn().mockImplementation(async (text, conversationId) => ({
          getResult: jest.fn().mockImplementation(async () => {
            // If conversationId is provided, use it (continuing conversation)
            // If not provided, create a new one (new conversation)
            const resultConversationId = conversationId || Math.floor(Math.random() * 1000);

            return {
              reply: [{ character: 'Fora', text: 'Test response', delay: 1000 }],
              skills: ['test'],
              theme: 'Test Theme',
              conversationId: resultConversationId
            };
          })
        }))
      };

      return workflowMethods;
    }),
  },
  WorkflowQueue: jest.fn().mockImplementation(() => ({
    name: 'test-queue',
    options: {},
  })),
}));

// Mock Google Generative AI
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn(),
}));

// Mock the new services
jest.mock('../src/services/GeminiLLMService', () => ({
  GeminiLLMService: jest.fn().mockImplementation(() => ({
    generate: jest.fn(),
  })),
}));

jest.mock('../src/core/PromptService', () => ({
  PromptService: {
    getSystemPrompt: jest.fn(),
    getAvailablePrompts: jest.fn(),
    clearCache: jest.fn(),
    getCacheSize: jest.fn(),
  },
}));

jest.mock('../src/core/ConversationService', () => ({
  ConversationService: {
    createConversation: jest.fn().mockImplementation(() => {
      const MoodService = require('../src/core/MoodService').MoodService;
      const moods = MoodService.generateRandomMoods();
      return Promise.resolve({
        id: 'mock-conversation-id',
        created_at: new Date(),
        updated_at: new Date(),
        theme: null,
        skills: null,
        last_user_activity: new Date(),
        engagement_level: 1.0,
        character_moods: JSON.stringify(moods)
      });
    }),
    createConversationForSession: jest.fn().mockResolvedValue({
      id: 'mock-conversation-id',
      created_at: new Date(),
      updated_at: new Date(),
      theme: null,
      skills: null,
      last_user_activity: new Date(),
      engagement_level: 1.0
    }),
    addMessage: jest.fn().mockResolvedValue({
      id: 'mock-message-id',
      conversation_id: 'mock-conversation-id',
      role: 'user',
      content: 'test message',
      created_at: new Date()
    }),
    checkMessageExists: jest.fn().mockResolvedValue(false),
    getConversationMessages: jest.fn().mockResolvedValue([]),
    getConversation: jest.fn().mockResolvedValue({
      id: 'mock-conversation-id',
      created_at: new Date(),
      updated_at: new Date(),
      theme: null,
      skills: null,
      last_user_activity: new Date(),
      engagement_level: 1.0
    }),
    getConversationBySession: jest.fn().mockResolvedValue({
      id: 'mock-conversation-id',
      created_at: new Date(),
      updated_at: new Date(),
      theme: null,
      skills: null,
      last_user_activity: new Date(),
      engagement_level: 1.0
    }),
    getSessionsForConversation: jest.fn().mockResolvedValue([]),
    getActiveSessionsForUser: jest.fn().mockResolvedValue([]),
    deleteConversation: jest.fn().mockResolvedValue(undefined),
    getRecentConversations: jest.fn().mockResolvedValue([]),
    getDelayedThoughts: jest.fn().mockResolvedValue([]),
    updateUserActivity: jest.fn().mockResolvedValue(undefined),
    updateEngagementLevel: jest.fn().mockResolvedValue(undefined),
    getLastUserActivity: jest.fn().mockResolvedValue(new Date()),
    getConversationWithSessions: jest.fn().mockResolvedValue({
      conversation: {
        id: 'mock-conversation-id',
        created_at: new Date(),
        updated_at: new Date(),
        theme: null,
        skills: null,
        last_user_activity: new Date(),
        engagement_level: 1.0
      },
      sessions: [],
      messages: []
    }),
    transferConversationToSession: jest.fn().mockResolvedValue(undefined),
    updateMessage: jest.fn().mockResolvedValue(undefined),
    updateCharacterMoods: jest.fn().mockResolvedValue(undefined),
    updateConversationMetadata: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('../src/core/MessageQueueService', () => ({
  MessageQueueService: {
    enqueueMessage: jest.fn(),
    getPendingMessages: jest.fn(),
    getReadyMessages: jest.fn(),
    updateMessageStatus: jest.fn(),
    checkSimilarity: jest.fn(),
    cancelMessage: jest.fn(),
    cancelAllMessages: jest.fn(),
    cleanupOldMessages: jest.fn(),
    getQueueStats: jest.fn(),
    resetStuckMessages: jest.fn(),
    getQueueDebugInfo: jest.fn(),
    forceProcessReadyMessages: jest.fn(),
  },
}));

jest.mock('../src/core/ConversationDecayService', () => ({
  ConversationDecayService: {
    calculateDelayMultiplier: jest.fn().mockReturnValue(1.0),
    calculateEngagementLevel: jest.fn().mockReturnValue(1.0),
    updateConversationEngagement: jest.fn().mockResolvedValue(undefined),
    applyDecayToDelay: jest.fn().mockReturnValue(1000),
    getDecayStatus: jest.fn().mockResolvedValue({
      engagementLevel: 1.0,
      delayMultiplier: 1.0,
      lastActivity: new Date(),
      shouldTimeout: false
    }),
    shouldTimeoutConversation: jest.fn().mockReturnValue(false),
  },
}));

jest.mock('../src/core/SessionService', () => ({
  SessionService: {
    createSession: jest.fn().mockResolvedValue({
      id: 'mock-session-id',
      user_identifier: 'test-user',
      channel: 'web',
      created_at: new Date(),
      updated_at: new Date(),
      last_activity: new Date(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
      metadata: {},
      conversation_id: null
    }),
    getSession: jest.fn().mockResolvedValue({
      id: 'mock-session-id',
      user_identifier: 'test-user',
      channel: 'web',
      created_at: new Date(),
      updated_at: new Date(),
      last_activity: new Date(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
      metadata: {},
      conversation_id: null
    }),
    getSessionByUserAndChannel: jest.fn().mockResolvedValue({
      id: 'mock-session-id',
      user_identifier: 'test-user',
      channel: 'web',
      created_at: new Date(),
      updated_at: new Date(),
      last_activity: new Date(),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
      metadata: {},
      conversation_id: null
    }),
    updateSessionActivity: jest.fn().mockResolvedValue(undefined),
    updateSessionConversation: jest.fn().mockResolvedValue(undefined),
    updateSessionMetadata: jest.fn().mockResolvedValue(undefined),
    deleteSession: jest.fn().mockResolvedValue(undefined),
    cleanupExpiredSessions: jest.fn().mockResolvedValue(1),
    extendSession: jest.fn().mockResolvedValue(undefined),
    getSessionsByUser: jest.fn().mockResolvedValue([]),
  },
}));

jest.mock('../src/core/SessionCleanupService', () => ({
  SessionCleanupService: {
    getCleanupStats: jest.fn().mockResolvedValue({
      totalSessions: 1,
      activeSessions: 1,
      totalConversations: 1,
      totalMessages: 1,
      expiredSessions: 0,
      orphanedConversations: 0,
      oldMessages: 0
    }),
    manualCleanup: jest.fn().mockResolvedValue({
      expiredSessions: 1,
      orphanedConversations: 0,
      oldMessages: 0,
      messageQueueEntries: 0
    }),
    cleanupExpiredSessions: jest.fn().mockResolvedValue(1),
    cleanupOrphanedConversations: jest.fn().mockResolvedValue(0),
    cleanupOldMessages: jest.fn().mockResolvedValue(0),
    startCleanupService: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('../src/operations', () => ({
  ForaChat: {
    createSession: jest.fn(),
    getSession: jest.fn(),
    cleanupExpiredSessions: jest.fn(),
    chatWorkflow: jest.fn(),
    createConversation: jest.fn(),
    addMessage: jest.fn(),
    getSystemPrompt: jest.fn(),
    setLLMService: jest.fn(),
  },
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.GEMINI_API_KEY = 'test-api-key';
process.env.DBOS_DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
